#!/usr/bin/env python3
import os
import glob

def test_patterns():
    """Test what files would be included with the current patterns"""
    
    # Simulate the patterns
    exclude_all = "**"
    include_authorizer = "authorizer/**"
    
    print("Files that would be included in authorizer package:")
    print("=" * 50)
    
    # Get all files in authorizer directory
    authorizer_files = glob.glob("authorizer/**", recursive=True)
    for file in sorted(authorizer_files):
        if os.path.isfile(file):
            print(f"  {file}")
    
    print("\nDirectory structure in authorizer:")
    print("=" * 50)
    for root, dirs, files in os.walk("authorizer"):
        level = root.replace("authorizer", "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = " " * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")

if __name__ == "__main__":
    test_patterns()
