def generate_policy(principal_id: str, effect: str, resources: list | str,
                    context: dict | None = None) -> dict:
    """Generate IAM policy for API Gateway custom authorizer.

    Resource may be a single ARN string or a list of ARNs.
    """
    policy = {
        "principalId": principal_id,
        "policyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "execute-api:Invoke",
                    "Effect": effect,
                    "Resource": resources,
                }
            ]
        }
    }

    if context:
        policy["context"] = {k: str(v) for k, v in context.items()}

    return policy
