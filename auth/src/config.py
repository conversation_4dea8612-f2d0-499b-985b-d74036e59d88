from pydantic_settings import BaseSettings, SettingsConfigDict


class Config(BaseSettings):
    jwt_domain: str                # e.g., https://fingermark.au.auth0.com/
    jwt_audience: str | list[str]  # e.g., single audience or JSON list of audiences

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix='API_GATEWAY_AUTHORIZER_'
    )
